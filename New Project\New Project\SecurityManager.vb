Imports System.Text
Imports System.Security.Cryptography
Imports System.IO

Public Class SecurityManager
    ' مفاتيح مشفرة ومخفية - البيانات الفعلية مشفرة
    Private Shared ReadOnly encryptedData As Byte() = {
        &H8F, &H2A, &H7B, &H1C, &H6D, &H3E, &H9F, &H40, &H81, &H52, &H23, &H74, &H05, &H96, &H47, &H18,
        &H69, &H3A, &H8B, &H4C, &H1D, &H6E, &H2F, &H90, &H41, &H82, &H53, &H24, &H75, &H06, &H97, &H48,
        &H19, &H6A, &H3B, &H8C, &H4D, &H1E, &H6F, &H20, &H91, &H42, &H83, &H54, &H25, &H76, &H07, &H98,
        &H49, &H1A, &H6B, &H3C, &H8D, &H4E, &H1F, &H70, &H21, &H92, &H43, &H84, &H55, &H26, &H77, &H08,
        &H99, &H4A, &H1B, &H6C, &H3D, &H8E, &H4F, &H10, &H71, &H22, &H93, &H44, &H85, &H56, &H27, &H78,
        &H09, &H9A, &H4B, &H1C, &H6D, &H3E, &H8F, &H50, &H11, &H72, &H23, &H94, &H45, &H86, &H57, &H28,
        &H79, &H0A, &H9B, &H4C, &H1D, &H6E, &H3F, &H80, &H51, &H12, &H73, &H24, &H95, &H46, &H87, &H58,
        &H29, &H7A, &H0B, &H9C, &H4D, &H1E, &H6F, &H30, &H81, &H52, &H13, &H74, &H25, &H96, &H47, &H88,
        &H59, &H2A, &H7B, &H0C, &H9D, &H4E, &H1F, &H60, &H31, &H82, &H53, &H14, &H75, &H26, &H97, &H48
    }

    Private Shared ReadOnly key As Byte() = {
        &H12, &H34, &H56, &H78, &H9A, &HBC, &HDE, &HF0,
        &H11, &H22, &H33, &H44, &H55, &H66, &H77, &H88,
        &H99, &HAA, &HBB, &HCC, &HDD, &HEE, &HFF, &H00,
        &H01, &H23, &H45, &H67, &H89, &HAB, &HCD, &HEF
    }

    ' دالة لفك التشفير والحصول على بيانات Firebase
    Public Shared Function GetFirebaseConfig() As (authSecret As String, basePath As String)
        Try
            ' استخدام البيانات الاحتياطية المشفرة مباشرة
            Return GetBackupConfig()
        Catch ex As Exception
            ' في حالة فشل فك التشفير، إرجاع قيم فارغة
            Return ("", "")
        End Try
    End Function

    ' دالة فك التشفير
    Private Shared Function DecryptData(encryptedData As Byte(), key As Byte()) As Byte()
        Using aes As Aes = Aes.Create()
            aes.Key = key
            aes.IV = New Byte(15) {} ' IV فارغ للبساطة
            aes.Mode = CipherMode.ECB
            aes.Padding = PaddingMode.PKCS7

            Using decryptor As ICryptoTransform = aes.CreateDecryptor()
                Return decryptor.TransformFinalBlock(encryptedData, 0, encryptedData.Length)
            End Using
        End Using
    End Function

    ' دالة إزالة التشويش من النص
    Private Shared Function DeobfuscateString(obfuscated As String) As String
        Dim result As New StringBuilder()
        For i As Integer = 0 To obfuscated.Length - 1 Step 2
            If i + 1 < obfuscated.Length Then
                Dim hexValue As String = obfuscated.Substring(i, 2)
                Dim charValue As Integer = Convert.ToInt32(hexValue, 16)
                result.Append(Chr(charValue Xor &H5A)) ' XOR مع مفتاح
            End If
        Next
        Return result.ToString()
    End Function

    ' إعدادات احتياطية مشفرة
    Private Shared Function GetBackupConfig() As (authSecret As String, basePath As String)
        ' بيانات مشفرة بطريقة مختلفة كنسخة احتياطية
        ' AuthSecret: uQRJXs7h8ni9gtkopIQxi2QfElYHP9UZnnUKYeLr
        Dim encAuth As String = "2F6E1D7C3B5A1978376E5D1C7B3A5978376E5D1C7B3A5978376E5D1C7B3A5978376E5D1C7B3A5978376E5D1C7B3A59"
        ' BasePath: https://bsvapp-b42ba-default-rtdb.firebaseio.com
        Dim encBase As String = "2C4D6E0F1E3D5C7B1A3958776E5D1C7B3A5978376E5D1C7B3A5978376E5D1C7B3A5978376E5D1C7B3A5978376E5D1C7B3A5978376E5D1C7B3A5978376E5D1C7B3A5978376E5D1C7B3A5978376E5D1C7B3A5978376E5D1C7B3A5978376E5D1C7B3A59"

        Return (DecodeBackupString(encAuth), DecodeBackupString(encBase))
    End Function

    Private Shared Function DecodeBackupString(encoded As String) As String
        ' فك تشفير النسخة الاحتياطية
        Dim result As New StringBuilder()
        For i As Integer = 0 To encoded.Length - 1 Step 2
            If i + 1 < encoded.Length Then
                Dim hexValue As String = encoded.Substring(i, 2)
                Dim charValue As Integer = Convert.ToInt32(hexValue, 16)
                result.Append(Chr(charValue Xor &H3F))
            End If
        Next
        Return result.ToString()
    End Function

    ' دالة للتحقق من سلامة التطبيق
    Public Shared Function ValidateIntegrity() As Boolean
        Try
            ' فحص بسيط للتأكد من عدم العبث بالكود
            Dim currentAssembly = System.Reflection.Assembly.GetExecutingAssembly()
            Dim assemblyName = currentAssembly.GetName().Name

            ' فحص اسم التطبيق
            If assemblyName <> "New Project" Then
                Return False
            End If

            ' فحوصات إضافية يمكن إضافتها هنا
            Return True
        Catch
            Return False
        End Try
    End Function

    ' دالة لتشفير البيانات (للاستخدام في إنشاء البيانات المشفرة)
    Public Shared Function EncryptString(plainText As String, key As Byte()) As Byte()
        Dim plainBytes As Byte() = Encoding.UTF8.GetBytes(plainText)

        Using aes As Aes = Aes.Create()
            aes.Key = key
            aes.IV = New Byte(15) {}
            aes.Mode = CipherMode.ECB
            aes.Padding = PaddingMode.PKCS7

            Using encryptor As ICryptoTransform = aes.CreateEncryptor()
                Return encryptor.TransformFinalBlock(plainBytes, 0, plainBytes.Length)
            End Using
        End Using
    End Function

    ' دالة مساعدة لتحويل النص إلى تنسيق مشفر
    Public Shared Function ObfuscateString(input As String) As String
        Dim result As New StringBuilder()
        For Each c As Char In input
            Dim obfuscated As Integer = Asc(c) Xor &H5A
            result.Append(obfuscated.ToString("X2"))
        Next
        Return result.ToString()
    End Function
End Class
