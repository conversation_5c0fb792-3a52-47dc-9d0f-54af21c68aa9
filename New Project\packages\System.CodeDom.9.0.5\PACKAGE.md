## About

<!-- A description of the package and where one can find more documentation -->

Provides functionality for dynamically generating and compiling source code using the Code Document Object Model (CodeDOM).

It allows developers to represent code in a language-agnostic format and then generate code in multiple languages, such as C# and VB.NET.
The primary use cases include creating dynamic code generation tools, runtime code generation, and facilitating code analysis or transformation.

For a new modern development consider using the [.NET Compiler Platform SDK](https://learn.microsoft.com/dotnet/csharp/roslyn-sdk/), in particular [Roslyn source generators](https://learn.microsoft.com/dotnet/csharp/roslyn-sdk/source-generators-overview#get-started-with-source-generators).

## Key Features

<!-- The key features of this package -->

* Write code using a common object model that can be translated into multiple programming languages.
* Generate and compile code at runtime based on the CodeDOM.

## How to Use

<!-- A compelling example on how to use this package with code, as well as any specific guidelines for when to use the package -->

Generating and compiling C# code:

```csharp
using System.CodeDom;
using System.CodeDom.Compiler;
using Microsoft.CSharp;

// Create a new CodeCompileUnit to hold the code
var compileUnit = new CodeCompileUnit();

// Create a namespace
var codeNamespace = new CodeNamespace("MyNamespace");
compileUnit.Namespaces.Add(codeNamespace);

// Create a class
var classDeclaration = new CodeTypeDeclaration("MyClass")
{
    IsClass = true
};
codeNamespace.Types.Add(classDeclaration);

// Add a simple method to the class
var method = new CodeMemberMethod
{
    Name = "HelloWorld",
    ReturnType = new CodeTypeReference(typeof(void)),
};
classDeclaration.Members.Add(method);

var methodInvocation = new CodeMethodInvokeExpression(new CodeTypeReferenceExpression("Console"),
                                                      "WriteLine",
                                                      new CodePrimitiveExpression("Hello, World!"));
method.Statements.Add(methodInvocation);

// Generate C# code from the CodeDOM structure
CodeDomProvider provider = new CSharpCodeProvider();

using (var writer = new StringWriter())
{
    var codeGenereationOptions = new CodeGeneratorOptions()
    {
        BlankLinesBetweenMembers = false,
        IndentString = "  ",
    };

    provider.GenerateCodeFromCompileUnit(compileUnit, writer, codeGenereationOptions);
    Console.WriteLine(writer.GetStringBuilder().ToString());
}
```

This example generates:

```csharp
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace MyNamespace {

  public class MyClass {
    private void HelloWorld() {
      Console.WriteLine("Hello, World!");
    }
  }
}
```

## Main Types

<!-- The main types provided in this library -->

The main types provided by this library are:

* `System.CodeDom.CodeObject`
* `System.CodeDom.CodeCompileUnit`
* `System.CodeDom.CodeNamespace`
* `System.CodeDom.CodeTypeDeclaration`
* `System.CodeDom.CodeMemberMethod`
* `System.CodeDom.CodeTypeReference`
* `System.CodeDom.CodeMethodInvokeExpression`
* `System.CodeDom.CodeTypeReferenceExpression`
* `System.CodeDom.CodePrimitiveExpression`
* `System.CodeDom.Compiler.CodeDomProvider`
* `System.CodeDom.Compiler.CodeGeneratorOptions`
* `Microsoft.CSharp.CSharpCodeProvider`
* `Microsoft.VisualBasic.VBCodeProvider`

## Additional Documentation

<!-- Links to further documentation. Remove conceptual documentation if not available for the library. -->

* [API documentation](https://learn.microsoft.com/dotnet/api/system.codedom)
* [Compile and generate dynamic source code](https://learn.microsoft.com/dotnet/framework/reflection-and-codedom/dynamic-source-code-generation-and-compilation)

## Feedback & Contributing

<!-- How to provide feedback on this package and contribute to it -->

System.CodeDom is released as open source under the [MIT license](https://licenses.nuget.org/MIT).
Bug reports and contributions are welcome at [the GitHub repository](https://github.com/dotnet/runtime).
