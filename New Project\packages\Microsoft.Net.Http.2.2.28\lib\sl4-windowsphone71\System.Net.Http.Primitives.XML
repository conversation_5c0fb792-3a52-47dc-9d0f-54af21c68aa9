<?xml version="1.0"?>
<doc>
    <assembly>
        <name>System.Net.Http.Primitives</name>
    </assembly>
    <members>
        <member name="T:System.Net.DecompressionMethods">
            <summary>
            Represents the file compression and decompression encoding format to be used to compress the data received in response to an <see cref="T:System.Net.HttpWebRequest" />.
            </summary>
        </member>
        <member name="F:System.Net.DecompressionMethods.None">
            <summary>
            Do not use compression.
            </summary>
        </member>
        <member name="F:System.Net.DecompressionMethods.GZip">
            <summary>
            Use the gZip compression-decompression algorithm.
            </summary>
        </member>
        <member name="F:System.Net.DecompressionMethods.Deflate">
            <summary>
            Use the deflate compression-decompression algorithm.
            </summary>
        </member>
        <member name="T:System.Net.IWebProxy">
            <summary>
            Provides the base interface for implementation of proxy access for the <see cref="T:System.Net.WebRequest" /> class.
            </summary>
        </member>
        <member name="M:System.Net.IWebProxy.GetProxy(System.Uri)">
            <summary>
            Returns the URI of a proxy.
            </summary>
            <param name="destination">A <see cref="T:System.Uri" /> that specifies the requested Internet resource. </param>
            <returns>A <see cref="T:System.Uri" /> instance that contains the URI of the proxy used to contact <paramref name="destination" />.</returns>
        </member>
        <member name="M:System.Net.IWebProxy.IsBypassed(System.Uri)">
            <summary>
            Indicates that the proxy should not be used for the specified host.
            </summary>
            <param name="host">The <see cref="T:System.Uri" /> of the host to check for proxy use. </param>
            <returns>true if the proxy server should not be used for <paramref name="host" />; otherwise, false.</returns>
        </member>
        <member name="P:System.Net.IWebProxy.Credentials">
            <summary>
            The credentials to submit to the proxy server for authentication.
            </summary>
            <returns>An <see cref="T:System.Net.ICredentials" /> instance that contains the credentials that are needed to authenticate a request to the proxy server.</returns>
        </member>
        <member name="T:System.Net.TransportContext">
            <summary>
            The System.Net.TransportContext class provides additional context about the underlying transport layer.
            </summary>
        </member>
    </members>
</doc>
