﻿Imports System.IO
Imports FireSharp.Config
Imports FireSharp.Interfaces
Imports FireSharp.Response
Imports System.Management

Public Class Form1

    Dim config As IFirebaseConfig = New FirebaseConfig With {
        .AuthSecret = "uQRJXs7h8ni9gtkopIQxi2QfElYHP9UZnnUKYeLr",
        .BasePath = "https://bsvapp-b42ba-default-rtdb.firebaseio.com"
    }


    Dim client As IFirebaseClient

    Private Async Sub Form1_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            client = New FireSharp.FirebaseClient(config)
            Dim hwid As String = GenerateHWID()
            txtHWID.Text = hwid

            Await LoadLicenses() ' جلب التراخيص عند فتح الفورم
        Catch ex As Exception
            MessageBox.Show("فشل الاتصال بـ Firebase: " & ex.Message)
        End Try
    End Sub

    Private Async Function LoadLicenses() As Task
        Try
            Dim response As FirebaseResponse = Await client.GetAsync("authorizedHWIDs")
            Dim data = response.ResultAs(Of Dictionary(Of String, Boolean))()

            dgvLicenses.Rows.Clear()

            If data IsNot Nothing Then
                For Each pair In data
                    Dim hwid = pair.Key
                    Dim status = pair.Value
                    dgvLicenses.Rows.Add(hwid, If(status, "مفعل", "معطل"), If(status, "تعطيل", "تفعيل"))
                Next
            End If
        Catch ex As Exception
            MessageBox.Show("خطأ في جلب التراخيص: " & ex.Message)
        End Try
    End Function

    ' عند الضغط على زر داخل DataGridView (تغيير حالة الترخيص)
    Private Async Sub dgvLicenses_CellContentClick(sender As Object, e As DataGridViewCellEventArgs) Handles dgvLicenses.CellContentClick
        If e.RowIndex >= 0 AndAlso e.ColumnIndex = 2 Then ' عمود الأزرار (Action)
            Dim hwid = dgvLicenses.Rows(e.RowIndex).Cells(0).Value.ToString()
            Dim currentStatusText = dgvLicenses.Rows(e.RowIndex).Cells(1).Value.ToString()
            Dim newStatus As Boolean = (currentStatusText = "معطل") ' إذا معطل نفعّل، وإذا مفعل نعطّل

            Try
                Dim response = Await client.SetAsync("authorizedHWIDs/" & hwid, newStatus)
                If response.StatusCode = Net.HttpStatusCode.OK Then
                    MessageBox.Show("تم تحديث حالة الترخيص: " & hwid)
                    Await LoadLicenses() ' إعادة تحميل التراخيص لتحديث الجدول
                Else
                    MessageBox.Show("فشل في تحديث الترخيص.")
                End If
            Catch ex As Exception
                MessageBox.Show("خطأ في تحديث الترخيص: " & ex.Message)
            End Try
        End If
    End Sub

    ' توليد HWID
    Function GenerateHWID() As String
        Return GetHardDiskSerial() & "-" & GetCPUID()
    End Function

    Function GetHardDiskSerial() As String
        Try
            Dim searcher As New ManagementObjectSearcher("SELECT * FROM Win32_PhysicalMedia")
            For Each wmi_HD As ManagementObject In searcher.Get()
                If wmi_HD("SerialNumber") IsNot Nothing Then
                    Return wmi_HD("SerialNumber").ToString().Trim()
                End If
            Next
        Catch
        End Try
        Return "Unknown"
    End Function

    Function GetCPUID() As String
        Try
            Dim cpuID As String = ""
            Dim mc As New ManagementClass("win32_processor")
            Dim moc As ManagementObjectCollection = mc.GetInstances()
            For Each mo As ManagementObject In moc
                cpuID = mo.Properties("processorID").Value.ToString()
                Exit For
            Next
            Return cpuID
        Catch
        End Try
        Return "Unknown"
    End Function

    Private Async Sub btnRegister_Click(sender As Object, e As EventArgs) Handles btnRegister.Click
        Dim hwid = txtHWID.Text.Trim()
        If hwid <> "" AndAlso hwid <> "Unknown-Unknown" Then
            Try
                Dim response = Await client.SetAsync("authorizedHWIDs/" & hwid, True)
                If response.StatusCode = Net.HttpStatusCode.OK Then
                    MessageBox.Show("تم تسجيل الجهاز بنجاح.")
                    Await LoadLicenses() ' تحديث القائمة بعد التسجيل
                Else
                    MessageBox.Show("فشل في التسجيل.")
                End If
            Catch ex As Exception
                MessageBox.Show("خطأ أثناء التسجيل: " & ex.Message)
            End Try
        Else
            MessageBox.Show("HWID غير صالح.")
        End If
    End Sub



    Private Sub Button2_Click(sender As Object, e As EventArgs) Handles Button2.Click
        Dim frm As New Client()
        frm.ShowDialog()
    End Sub
End Class
