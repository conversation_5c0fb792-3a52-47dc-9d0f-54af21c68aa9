Imports System.Text

' ملف مؤقت لحساب التشفير - يمكن حذفه بعد الانتهاء
Public Class CalculateEncryption
    Public Shared Sub Main()
        ' البيانات الأصلية
        Dim authSecret As String = "uQRJXs7h8ni9gtkopIQxi2QfElYHP9UZnnUKYeLr"
        Dim basePath As String = "https://bsvapp-b42ba-default-rtdb.firebaseio.com"
        
        ' حساب التشفير للـ AuthSecret
        Dim encAuth As New StringBuilder()
        For Each c As Char In authSecret
            Dim encoded As Integer = Asc(c) Xor &H3F
            encAuth.Append(encoded.ToString("X2"))
        Next
        
        ' حساب التشفير للـ BasePath
        Dim encBase As New StringBuilder()
        For Each c As Char In basePath
            Dim encoded As Integer = Asc(c) Xor &H3F
            encBase.Append(encoded.ToString("X2"))
        Next
        
        Console.WriteLine("AuthSecret مشفر:")
        Console.WriteLine(encAuth.ToString())
        Console.WriteLine()
        Console.WriteLine("BasePath مشفر:")
        Console.WriteLine(encBase.ToString())
        
        ' اختبار فك التشفير
        Console.WriteLine()
        Console.WriteLine("اختبار فك التشفير:")
        Console.WriteLine("AuthSecret: " & DecodeString(encAuth.ToString()))
        Console.WriteLine("BasePath: " & DecodeString(encBase.ToString()))
    End Sub
    
    Private Shared Function DecodeString(encoded As String) As String
        Dim result As New StringBuilder()
        For i As Integer = 0 To encoded.Length - 1 Step 2
            If i + 1 < encoded.Length Then
                Dim hexValue As String = encoded.Substring(i, 2)
                Dim charValue As Integer = Convert.ToInt32(hexValue, 16)
                result.Append(Chr(charValue Xor &H3F))
            End If
        Next
        Return result.ToString()
    End Function
End Class
