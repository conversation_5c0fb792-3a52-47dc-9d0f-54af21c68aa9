Imports System.Text

' هذا الكلاس لتوليد البيانات المشفرة - يمكن حذفه بعد الانتهاء
Public Class EncryptionHelper
    Public Shared Sub GenerateEncryptedData()
        ' البيانات الأصلية
        Dim authSecret As String = "uQRJXs7h8ni9gtkopIQxi2QfElYHP9UZnnUKYeLr"
        Dim basePath As String = "https://bsvapp-b42ba-default-rtdb.firebaseio.com"
        
        ' تشفير البيانات
        Dim obfuscatedAuth As String = SecurityManager.ObfuscateString(authSecret)
        Dim obfuscatedBase As String = SecurityManager.ObfuscateString(basePath)
        
        ' دمج البيانات
        Dim combinedData As String = obfuscatedAuth & "|" & obfuscatedBase
        
        ' مفتاح التشفير
        Dim key As Byte() = {
            &H12, &H34, &H56, &H78, &H9A, &HBC, &HDE, &HF0,
            &H11, &H22, &H33, &H44, &H55, &H66, &H77, &H88,
            &H99, &HAA, &HBB, &HCC, &HDD, &HEE, &HFF, &H00,
            &H01, &H23, &H45, &H67, &H89, &HAB, &HCD, &HEF
        }
        
        ' تشفير البيانات
        Dim encryptedBytes As Byte() = SecurityManager.EncryptString(combinedData, key)
        
        ' طباعة النتيجة للاستخدام في SecurityManager
        Console.WriteLine("البيانات المشفرة:")
        Console.Write("Private Shared ReadOnly encryptedData As Byte() = {")
        For i As Integer = 0 To encryptedBytes.Length - 1
            If i > 0 Then Console.Write(", ")
            If i Mod 16 = 0 Then Console.WriteLine()
            Console.Write("&H" & encryptedBytes(i).ToString("X2"))
        Next
        Console.WriteLine("}")
        
        ' إنشاء بيانات احتياطية
        Dim backupAuth As String = ""
        Dim backupBase As String = ""
        
        For Each c As Char In authSecret
            Dim encoded As Integer = Asc(c) Xor &H3F
            backupAuth &= encoded.ToString("X2")
        Next
        
        For Each c As Char In basePath
            Dim encoded As Integer = Asc(c) Xor &H3F
            backupBase &= encoded.ToString("X2")
        Next
        
        Console.WriteLine()
        Console.WriteLine("البيانات الاحتياطية:")
        Console.WriteLine("Auth: " & backupAuth)
        Console.WriteLine("Base: " & backupBase)
    End Sub
End Class
