﻿Imports FireSharp.Config
Imports FireSharp.Interfaces
Imports FireSharp.Response
Imports System.Management
Imports System.Windows.Forms

Public Class Client

    Dim config As IFirebaseConfig = New FirebaseConfig With {
        .AuthSecret = "uQRJXs7h8ni9gtkopIQxi2QfElYHP9UZnnUKYeLr",
        .BasePath = "https://bsvapp-b42ba-default-rtdb.firebaseio.com"
    }

    Dim client As IFirebaseClient
    Dim hwid As String
    Dim licenseTimer As New Timer()

    Private Async Sub Client_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            client = New FireSharp.FirebaseClient(config)
            hwid = GenerateHWID()

            Dim valid As Boolean = Await CheckLicenseAsync()
            If Not valid Then
                MessageBox.Show("هذا الجهاز غير مرخص أو الترخيص معطل. سيتم إغلاق البرنامج.", "غير مصرح", MessageBoxButtons.OK, MessageBoxIcon.Error)
                ExitApplication()
                Return
            End If

            ' ضبط التايمر لإعادة التحقق كل دقيقة (60000 مللي ثانية)
            AddHandler licenseTimer.Tick, AddressOf licenseTimer_Tick
            licenseTimer.Interval = 30000
            licenseTimer.Start()

            ' يمكن هنا متابعة تشغيل التطبيق
            ' مثال: MessageBox.Show("مرحباً بك!")

        Catch ex As Exception
            MessageBox.Show("تعذر التحقق من الترخيص: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            ExitApplication()
        End Try
    End Sub

    Private Async Sub licenseTimer_Tick(sender As Object, e As EventArgs)
        Dim valid As Boolean = Await CheckLicenseAsync()
        If Not valid Then
            MessageBox.Show("تم تعطيل الترخيص، سيتم إغلاق البرنامج.", "انتهاء الترخيص", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            ExitApplication()
        End If
    End Sub

    Private Async Function CheckLicenseAsync() As Threading.Tasks.Task(Of Boolean)
        Try
            Dim response As FirebaseResponse = Await client.GetAsync("authorizedHWIDs/" & hwid)
            If response.Body = "null" Then Return False

            Dim status As Boolean = False
            Boolean.TryParse(response.Body, status)
            Return status
        Catch
            Return False
        End Try
    End Function

    Private Sub ExitApplication()
        licenseTimer.Stop()
        Application.Exit()
    End Sub

    Function GenerateHWID() As String
        Return GetHardDiskSerial() & "-" & GetCPUID()
    End Function

    Function GetHardDiskSerial() As String
        Try
            Dim searcher As New ManagementObjectSearcher("SELECT * FROM Win32_PhysicalMedia")
            For Each wmi_HD As ManagementObject In searcher.Get()
                If wmi_HD("SerialNumber") IsNot Nothing Then
                    Return wmi_HD("SerialNumber").ToString().Trim()
                End If
            Next
        Catch
        End Try
        Return "Unknown"
    End Function

    Function GetCPUID() As String
        Try
            Dim cpuID As String = ""
            Dim mc As New ManagementClass("win32_processor")
            Dim moc As ManagementObjectCollection = mc.GetInstances()
            For Each mo As ManagementObject In moc
                cpuID = mo.Properties("processorID").Value.ToString()
                Exit For
            Next
            Return cpuID
        Catch
        End Try
        Return "Unknown"
    End Function

End Class
